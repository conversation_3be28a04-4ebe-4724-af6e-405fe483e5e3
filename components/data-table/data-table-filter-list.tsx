"use client";

import type { Column, ColumnMeta, Table } from "@tanstack/react-table";
import {
	CalendarIcon,
	Check,
	ChevronsUpDown,
	GripVertical,
	ListFilter,
	Trash2,
} from "lucide-react";
import { parseAsStringEnum, useQueryState } from "nuqs";
import * as React from "react";

import { DataTableRangeFilter } from "@/components/data-table/data-table-range-filter";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from "@/components/ui/command";
import {
	Faceted,
	FacetedBadgeList,
	FacetedContent,
	FacetedEmpty,
	FacetedGroup,
	FacetedInput,
	FacetedItem,
	FacetedList,
	FacetedTrigger,
} from "@/components/ui/faceted";
import { Input } from "@/components/ui/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	Sortable,
	SortableContent,
	SortableItem,
	SortableItemHandle,
	SortableOverlay,
} from "@/components/ui/sortable";
import { dataTableConfig } from "@/lib/configs/data-table";
import { useDebouncedCallback } from "@/lib/hooks/use-debounced-callback";
import type {
	ExtendedColumnFilter,
	FilterOperator,
	JoinOperator,
} from "@/lib/types/data-table";
import {
	getDefaultFilterOperator,
	getFilterOperators,
} from "@/lib/utils/data-table";
import { formatDate } from "@/lib/utils/format-date";
import { generateId } from "@/lib/utils/id";
import { getFiltersStateParser } from "@/lib/utils/parsers";
import { cn } from "@/lib/utils/tailwind";

const FILTERS_KEY = "filters";
const JOIN_OPERATOR_KEY = "joinOperator";
const DEBOUNCE_MS = 300;
const THROTTLE_MS = 50;
const OPEN_MENU_SHORTCUT = "f";
const REMOVE_FILTER_SHORTCUTS = ["backspace", "delete"];

interface DataTableFilterListProps<TData>
	extends React.ComponentProps<typeof PopoverContent> {
	table: Table<TData>;
	debounceMs?: number;
	throttleMs?: number;
	shallow?: boolean;
}

export function DataTableFilterList<TData>({
	table,
	debounceMs = DEBOUNCE_MS,
	throttleMs = THROTTLE_MS,
	shallow = true,
	...props
}: DataTableFilterListProps<TData>) {
	const id = React.useId();
	const labelId = React.useId();
	const descriptionId = React.useId();
	const [open, setOpen] = React.useState(false);
	const addButtonRef = React.useRef<HTMLButtonElement>(null);

	const columns = React.useMemo(() => {
		return table
			.getAllColumns()
			.filter((column) => column.columnDef.enableColumnFilter);
	}, [table]);

	const [filters, setFilters] = useQueryState(
		FILTERS_KEY,
		getFiltersStateParser<TData>(columns.map((field) => field.id))
			.withDefault([])
			.withOptions({
				clearOnDefault: true,
				shallow,
				throttleMs,
			}),
	);
	const debouncedSetFilters = useDebouncedCallback(setFilters, debounceMs);

	const [joinOperator, setJoinOperator] = useQueryState(
		JOIN_OPERATOR_KEY,
		parseAsStringEnum(["and", "or"]).withDefault("and").withOptions({
			clearOnDefault: true,
			shallow,
		}),
	);

	const onFilterAdd = React.useCallback(() => {
		const column = columns[0];

		if (!column) return;

		debouncedSetFilters([
			...filters,
			{
				id: column.id as Extract<keyof TData, string>,
				value: "",
				variant: column.columnDef.meta?.variant ?? "text",
				operator: getDefaultFilterOperator(
					column.columnDef.meta?.variant ?? "text",
				),
				filterId: generateId({ length: 8 }),
			},
		]);
	}, [columns, filters, debouncedSetFilters]);

	const onFilterUpdate = React.useCallback(
		(
			filterId: string,
			updates: Partial<Omit<ExtendedColumnFilter<TData>, "filterId">>,
		) => {
			debouncedSetFilters((prevFilters) => {
				const updatedFilters = prevFilters.map((filter) => {
					if (filter.filterId === filterId) {
						return { ...filter, ...updates } as ExtendedColumnFilter<TData>;
					}
					return filter;
				});
				return updatedFilters;
			});
		},
		[debouncedSetFilters],
	);

	const onFilterRemove = React.useCallback(
		(filterId: string) => {
			const updatedFilters = filters.filter(
				(filter) => filter.filterId !== filterId,
			);
			void setFilters(updatedFilters);
			requestAnimationFrame(() => {
				addButtonRef.current?.focus();
			});
		},
		[filters, setFilters],
	);

	const onFiltersReset = React.useCallback(() => {
		void setFilters(null);
		void setJoinOperator("and");
	}, [setFilters, setJoinOperator]);

	React.useEffect(() => {
		function onKeyDown(event: KeyboardEvent) {
			if (
				event.target instanceof HTMLInputElement ||
				event.target instanceof HTMLTextAreaElement
			) {
				return;
			}

			if (
				event.key.toLowerCase() === OPEN_MENU_SHORTCUT &&
				!event.ctrlKey &&
				!event.metaKey &&
				!event.shiftKey
			) {
				event.preventDefault();
				setOpen(true);
			}

			if (
				event.key.toLowerCase() === OPEN_MENU_SHORTCUT &&
				event.shiftKey &&
				filters.length > 0
			) {
				event.preventDefault();
				onFilterRemove(filters[filters.length - 1]?.filterId ?? "");
			}
		}

		window.addEventListener("keydown", onKeyDown);
		return () => window.removeEventListener("keydown", onKeyDown);
	}, [filters, onFilterRemove]);

	const onTriggerKeyDown = React.useCallback(
		(event: React.KeyboardEvent<HTMLButtonElement>) => {
			if (
				REMOVE_FILTER_SHORTCUTS.includes(event.key.toLowerCase()) &&
				filters.length > 0
			) {
				event.preventDefault();
				onFilterRemove(filters[filters.length - 1]?.filterId ?? "");
			}
		},
		[filters, onFilterRemove],
	);

	return (
		<Sortable
			value={filters}
			onValueChange={setFilters}
			getItemValue={(item: ExtendedColumnFilter<TData>) => item.filterId}
		>
			<Popover open={open} onOpenChange={setOpen}>
				<PopoverTrigger asChild>
					<Button variant="outline" size="sm" onKeyDown={onTriggerKeyDown}>
						<ListFilter />
						Filter
						{filters.length > 0 && (
							<Badge
								variant="secondary"
								className="px-[5.12px] rounded-[3.2px] h-[18.24px] font-mono font-normal text-[10.4px]"
							>
								{filters.length}
							</Badge>
						)}
					</Button>
				</PopoverTrigger>
				<PopoverContent
					aria-describedby={descriptionId}
					aria-labelledby={labelId}
					className="flex flex-col gap-4.5 p-4 w-full sm:min-w-[380px] max-w-[var(--radix-popover-content-available-width)] origin-[var(--radix-popover-content-transform-origin)]"
					{...props}
				>
					<div className="flex flex-col gap-1">
						<h4 id={labelId} className="font-medium leading-none">
							{filters.length > 0 ? "Filters" : "No filters applied"}
						</h4>
						<p
							id={descriptionId}
							className={cn(
								"text-muted-foreground text-sm",
								filters.length > 0 && "sr-only",
							)}
						>
							{filters.length > 0
								? "Modify filters to refine your rows."
								: "Add filters to refine your rows."}
						</p>
					</div>
					{filters.length > 0 ? (
						<SortableContent asChild>
							<ul className="flex flex-col gap-2 p-1 max-h-[300px] overflow-y-auto">
								{filters.map((filter, index) => (
									<DataTableFilterItem<TData>
										key={filter.filterId}
										filter={filter}
										index={index}
										filterItemId={`${id}-filter-${filter.filterId}`}
										joinOperator={joinOperator}
										setJoinOperator={setJoinOperator}
										columns={columns}
										onFilterUpdate={onFilterUpdate}
										onFilterRemove={onFilterRemove}
									/>
								))}
							</ul>
						</SortableContent>
					) : null}
					<div className="flex items-center gap-2 w-full">
						<Button
							size="sm"
							className="rounded"
							ref={addButtonRef}
							onClick={onFilterAdd}
						>
							Add filter
						</Button>
						{filters.length > 0 ? (
							<Button
								variant="outline"
								size="sm"
								className="rounded"
								onClick={onFiltersReset}
							>
								Reset filters
							</Button>
						) : null}
					</div>
				</PopoverContent>
			</Popover>
			<SortableOverlay>
				<div className="flex items-center gap-2">
					<div className="bg-primary/10 rounded-sm min-w-[72px] h-8" />
					<div className="bg-primary/10 rounded-sm w-32 h-8" />
					<div className="bg-primary/10 rounded-sm w-32 h-8" />
					<div className="flex-1 bg-primary/10 rounded-sm min-w-36 h-8" />
					<div className="bg-primary/10 rounded-sm size-8 shrink-0" />
					<div className="bg-primary/10 rounded-sm size-8 shrink-0" />
				</div>
			</SortableOverlay>
		</Sortable>
	);
}

interface DataTableFilterItemProps<TData> {
	filter: ExtendedColumnFilter<TData>;
	index: number;
	filterItemId: string;
	joinOperator: JoinOperator;
	setJoinOperator: (value: JoinOperator) => void;
	columns: Column<TData>[];
	onFilterUpdate: (
		filterId: string,
		updates: Partial<Omit<ExtendedColumnFilter<TData>, "filterId">>,
	) => void;
	onFilterRemove: (filterId: string) => void;
}

function DataTableFilterItem<TData>({
	filter,
	index,
	filterItemId,
	joinOperator,
	setJoinOperator,
	columns,
	onFilterUpdate,
	onFilterRemove,
}: DataTableFilterItemProps<TData>) {
	const [showFieldSelector, setShowFieldSelector] = React.useState(false);
	const [showOperatorSelector, setShowOperatorSelector] = React.useState(false);
	const [showValueSelector, setShowValueSelector] = React.useState(false);

	const column = columns.find((column) => column.id === filter.id);

	const joinOperatorListboxId = `${filterItemId}-join-operator-listbox`;
	const fieldListboxId = `${filterItemId}-field-listbox`;
	const operatorListboxId = `${filterItemId}-operator-listbox`;
	const inputId = `${filterItemId}-input`;

	const columnMeta = column?.columnDef.meta;
	const filterOperators = getFilterOperators(filter.variant);

	const onItemKeyDown = React.useCallback(
		(event: React.KeyboardEvent<HTMLLIElement>) => {
			if (
				event.target instanceof HTMLInputElement ||
				event.target instanceof HTMLTextAreaElement
			) {
				return;
			}

			if (showFieldSelector || showOperatorSelector || showValueSelector) {
				return;
			}

			if (REMOVE_FILTER_SHORTCUTS.includes(event.key.toLowerCase())) {
				event.preventDefault();
				onFilterRemove(filter.filterId);
			}
		},
		[
			filter.filterId,
			showFieldSelector,
			showOperatorSelector,
			showValueSelector,
			onFilterRemove,
		],
	);

	if (!column) return null;

	return (
		<SortableItem value={filter.filterId} asChild>
			<li
				id={filterItemId}
				tabIndex={-1}
				className="flex items-center gap-2"
				onKeyDown={onItemKeyDown}
			>
				<div className="min-w-[72px] text-center">
					{index === 0 ? (
						<span className="text-muted-foreground text-sm">Where</span>
					) : index === 1 ? (
						<Select
							value={joinOperator}
							onValueChange={(value: JoinOperator) => setJoinOperator(value)}
						>
							<SelectTrigger
								aria-label="Select join operator"
								aria-controls={joinOperatorListboxId}
								className="rounded h-8 [&[data-size]]:h-8 lowercase"
							>
								<SelectValue placeholder={joinOperator} />
							</SelectTrigger>
							<SelectContent
								id={joinOperatorListboxId}
								position="popper"
								className="lowercase min-w-(--radix-select-trigger-width)"
							>
								{dataTableConfig.joinOperators.map((joinOperator) => (
									<SelectItem key={joinOperator} value={joinOperator}>
										{joinOperator}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					) : (
						<span className="text-muted-foreground text-sm">
							{joinOperator}
						</span>
					)}
				</div>
				<Popover open={showFieldSelector} onOpenChange={setShowFieldSelector}>
					<PopoverTrigger asChild>
						<Button
							aria-controls={fieldListboxId}
							variant="outline"
							size="sm"
							className="justify-between rounded w-32 font-normal"
						>
							<span className="truncate">
								{columns.find((column) => column.id === filter.id)?.columnDef
									.meta?.label ?? "Select field"}
							</span>
							<ChevronsUpDown className="opacity-50" />
						</Button>
					</PopoverTrigger>
					<PopoverContent
						id={fieldListboxId}
						align="start"
						className="p-0 w-40 origin-[var(--radix-popover-content-transform-origin)]"
					>
						<Command>
							<CommandInput placeholder="Search fields..." />
							<CommandList>
								<CommandEmpty>No fields found.</CommandEmpty>
								<CommandGroup>
									{columns.map((column) => (
										<CommandItem
											key={column.id}
											value={column.id}
											onSelect={(value) => {
												onFilterUpdate(filter.filterId, {
													id: value as Extract<keyof TData, string>,
													variant: column.columnDef.meta?.variant ?? "text",
													operator: getDefaultFilterOperator(
														column.columnDef.meta?.variant ?? "text",
													),
													value: "",
												});

												setShowFieldSelector(false);
											}}
										>
											<span className="truncate">
												{column.columnDef.meta?.label}
											</span>
											<Check
												className={cn(
													"ml-auto",
													column.id === filter.id ? "opacity-100" : "opacity-0",
												)}
											/>
										</CommandItem>
									))}
								</CommandGroup>
							</CommandList>
						</Command>
					</PopoverContent>
				</Popover>
				<Select
					open={showOperatorSelector}
					onOpenChange={setShowOperatorSelector}
					value={filter.operator}
					onValueChange={(value: FilterOperator) =>
						onFilterUpdate(filter.filterId, {
							operator: value,
							value:
								value === "isEmpty" || value === "isNotEmpty"
									? ""
									: filter.value,
						})
					}
				>
					<SelectTrigger
						aria-controls={operatorListboxId}
						className="rounded w-32 h-8 [&[data-size]]:h-8 lowercase"
					>
						<div className="truncate">
							<SelectValue placeholder={filter.operator} />
						</div>
					</SelectTrigger>
					<SelectContent
						id={operatorListboxId}
						className="origin-[var(--radix-select-content-transform-origin)]"
					>
						{filterOperators.map((operator) => (
							<SelectItem
								key={operator.value}
								value={operator.value}
								className="lowercase"
							>
								{operator.label}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
				<div className="flex-1 min-w-36">
					{onFilterInputRender({
						filter,
						inputId,
						column,
						columnMeta,
						onFilterUpdate,
						showValueSelector,
						setShowValueSelector,
					})}
				</div>
				<Button
					aria-controls={filterItemId}
					variant="outline"
					size="icon"
					className="rounded size-8"
					onClick={() => onFilterRemove(filter.filterId)}
				>
					<Trash2 />
				</Button>
				<SortableItemHandle asChild>
					<Button variant="outline" size="icon" className="rounded size-8">
						<GripVertical />
					</Button>
				</SortableItemHandle>
			</li>
		</SortableItem>
	);
}

function onFilterInputRender<TData>({
	filter,
	inputId,
	column,
	columnMeta,
	onFilterUpdate,
	showValueSelector,
	setShowValueSelector,
}: {
	filter: ExtendedColumnFilter<TData>;
	inputId: string;
	column: Column<TData>;
	columnMeta?: ColumnMeta<TData, unknown>;
	onFilterUpdate: (
		filterId: string,
		updates: Partial<Omit<ExtendedColumnFilter<TData>, "filterId">>,
	) => void;
	showValueSelector: boolean;
	setShowValueSelector: (value: boolean) => void;
}) {
	if (filter.operator === "isEmpty" || filter.operator === "isNotEmpty") {
		return (
			<div
				id={inputId}
				role="status"
				aria-label={`${columnMeta?.label} filter is ${
					filter.operator === "isEmpty" ? "empty" : "not empty"
				}`}
				aria-live="polite"
				className="bg-transparent dark:bg-input/30 border rounded w-full h-8"
			/>
		);
	}

	switch (filter.variant) {
		case "text":
		case "number":
		case "range": {
			if (
				(filter.variant === "range" && filter.operator === "isBetween") ||
				filter.operator === "isBetween"
			) {
				return (
					<DataTableRangeFilter
						filter={filter}
						column={column}
						inputId={inputId}
						onFilterUpdate={onFilterUpdate}
					/>
				);
			}

			const isNumber =
				filter.variant === "number" || filter.variant === "range";

			return (
				<Input
					id={inputId}
					type={isNumber ? "number" : filter.variant}
					aria-label={`${columnMeta?.label} filter value`}
					aria-describedby={`${inputId}-description`}
					inputMode={isNumber ? "numeric" : undefined}
					placeholder={columnMeta?.placeholder ?? "Enter a value..."}
					className="rounded w-full h-8"
					defaultValue={
						typeof filter.value === "string" ? filter.value : undefined
					}
					onChange={(event) =>
						onFilterUpdate(filter.filterId, {
							value: event.target.value,
						})
					}
				/>
			);
		}

		case "boolean": {
			if (Array.isArray(filter.value)) return null;

			const inputListboxId = `${inputId}-listbox`;

			return (
				<Select
					open={showValueSelector}
					onOpenChange={setShowValueSelector}
					value={filter.value}
					onValueChange={(value) =>
						onFilterUpdate(filter.filterId, {
							value,
						})
					}
				>
					<SelectTrigger
						id={inputId}
						aria-controls={inputListboxId}
						aria-label={`${columnMeta?.label} boolean filter`}
						className="rounded w-full h-8 [&[data-size]]:h-8"
					>
						<SelectValue placeholder={filter.value ? "True" : "False"} />
					</SelectTrigger>
					<SelectContent id={inputListboxId}>
						<SelectItem value="true">True</SelectItem>
						<SelectItem value="false">False</SelectItem>
					</SelectContent>
				</Select>
			);
		}

		case "select":
		case "multiSelect": {
			const inputListboxId = `${inputId}-listbox`;

			const multiple = filter.variant === "multiSelect";
			const selectedValues = multiple
				? Array.isArray(filter.value)
					? filter.value
					: []
				: typeof filter.value === "string"
					? filter.value
					: undefined;

			return (
				<Faceted
					open={showValueSelector}
					onOpenChange={setShowValueSelector}
					value={selectedValues}
					onValueChange={(value) => {
						onFilterUpdate(filter.filterId, {
							value,
						});
					}}
					multiple={multiple}
				>
					<FacetedTrigger asChild>
						<Button
							id={inputId}
							aria-controls={inputListboxId}
							aria-label={`${columnMeta?.label} filter value${multiple ? "s" : ""}`}
							variant="outline"
							size="sm"
							className="rounded w-full font-normal"
						>
							<FacetedBadgeList
								options={columnMeta?.options}
								placeholder={
									columnMeta?.placeholder ??
									`Select option${multiple ? "s" : ""}...`
								}
							/>
						</Button>
					</FacetedTrigger>
					<FacetedContent
						id={inputListboxId}
						className="w-[200px] origin-[var(--radix-popover-content-transform-origin)]"
					>
						<FacetedInput
							aria-label={`Search ${columnMeta?.label} options`}
							placeholder={columnMeta?.placeholder ?? "Search options..."}
						/>
						<FacetedList>
							<FacetedEmpty>No options found.</FacetedEmpty>
							<FacetedGroup>
								{columnMeta?.options?.map((option) => (
									<FacetedItem key={option.value} value={option.value}>
										{option.icon && <option.icon />}
										<span>{option.label}</span>
										{option.count && (
											<span className="ml-auto font-mono text-xs">
												{option.count}
											</span>
										)}
									</FacetedItem>
								))}
							</FacetedGroup>
						</FacetedList>
					</FacetedContent>
				</Faceted>
			);
		}

		case "date":
		case "dateRange": {
			const inputListboxId = `${inputId}-listbox`;

			const dateValue = Array.isArray(filter.value)
				? filter.value.filter(Boolean)
				: [filter.value, filter.value].filter(Boolean);

			const displayValue =
				filter.operator === "isBetween" && dateValue.length === 2
					? `${formatDate(new Date(Number(dateValue[0])))} - ${formatDate(
							new Date(Number(dateValue[1])),
						)}`
					: dateValue[0]
						? formatDate(new Date(Number(dateValue[0])))
						: "Pick a date";

			return (
				<Popover open={showValueSelector} onOpenChange={setShowValueSelector}>
					<PopoverTrigger asChild>
						<Button
							id={inputId}
							aria-controls={inputListboxId}
							aria-label={`${columnMeta?.label} date filter`}
							variant="outline"
							size="sm"
							className={cn(
								"justify-start rounded w-full font-normal text-left",
								!filter.value && "text-muted-foreground",
							)}
						>
							<CalendarIcon />
							<span className="truncate">{displayValue}</span>
						</Button>
					</PopoverTrigger>
					<PopoverContent
						id={inputListboxId}
						align="start"
						className="p-0 w-auto origin-[var(--radix-popover-content-transform-origin)]"
					>
						{filter.operator === "isBetween" ? (
							<Calendar
								aria-label={`Select ${columnMeta?.label} date range`}
								mode="range"
								captionLayout="dropdown"
								selected={
									dateValue.length === 2
										? {
												from: new Date(Number(dateValue[0])),
												to: new Date(Number(dateValue[1])),
											}
										: {
												from: new Date(),
												to: new Date(),
											}
								}
								onSelect={(date) => {
									onFilterUpdate(filter.filterId, {
										value: date
											? [
													(date.from?.getTime() ?? "").toString(),
													(date.to?.getTime() ?? "").toString(),
												]
											: [],
									});
								}}
							/>
						) : (
							<Calendar
								aria-label={`Select ${columnMeta?.label} date`}
								mode="single"
								captionLayout="dropdown"
								selected={
									dateValue[0] ? new Date(Number(dateValue[0])) : undefined
								}
								onSelect={(date) => {
									onFilterUpdate(filter.filterId, {
										value: (date?.getTime() ?? "").toString(),
									});
								}}
							/>
						)}
					</PopoverContent>
				</Popover>
			);
		}

		default:
			return null;
	}
}
