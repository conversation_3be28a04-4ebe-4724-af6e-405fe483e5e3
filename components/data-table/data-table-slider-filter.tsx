"use client";

import type { Column } from "@tanstack/react-table";
import { PlusCircle, XCircle } from "lucide-react";
import * as React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import { Slider } from "@/components/ui/slider";
import { cn } from "@/lib/utils/tailwind";

interface Range {
	min: number;
	max: number;
}

type RangeValue = [number, number];

function getIsValidRange(value: unknown): value is RangeValue {
	return (
		Array.isArray(value) &&
		value.length === 2 &&
		typeof value[0] === "number" &&
		typeof value[1] === "number"
	);
}

interface DataTableSliderFilterProps<TData> {
	column: Column<TData, unknown>;
	title?: string;
}

export function DataTableSliderFilter<TData>({
	column,
	title,
}: DataTableSliderFilterProps<TData>) {
	const id = React.useId();

	const columnFilterValue = getIsValidRange(column.getFilterValue())
		? (column.getFilterValue() as RangeValue)
		: undefined;

	const defaultRange = column.columnDef.meta?.range;
	const unit = column.columnDef.meta?.unit;

	const { min, max, step } = React.useMemo<Range & { step: number }>(() => {
		let minValue = 0;
		let maxValue = 100;

		if (defaultRange && getIsValidRange(defaultRange)) {
			[minValue, maxValue] = defaultRange;
		} else {
			const values = column.getFacetedMinMaxValues();
			if (values && Array.isArray(values) && values.length === 2) {
				const [facetMinValue, facetMaxValue] = values;
				if (
					typeof facetMinValue === "number" &&
					typeof facetMaxValue === "number"
				) {
					minValue = facetMinValue;
					maxValue = facetMaxValue;
				}
			}
		}

		const rangeSize = maxValue - minValue;
		const step =
			rangeSize <= 20
				? 1
				: rangeSize <= 100
					? Math.ceil(rangeSize / 20)
					: Math.ceil(rangeSize / 50);

		return { min: minValue, max: maxValue, step };
	}, [column, defaultRange]);

	const range = React.useMemo((): RangeValue => {
		return columnFilterValue ?? [min, max];
	}, [columnFilterValue, min, max]);

	const formatValue = React.useCallback((value: number) => {
		return value.toLocaleString(undefined, { maximumFractionDigits: 0 });
	}, []);

	const onFromInputChange = React.useCallback(
		(event: React.ChangeEvent<HTMLInputElement>) => {
			const numValue = Number(event.target.value);
			if (!Number.isNaN(numValue) && numValue >= min && numValue <= range[1]) {
				column.setFilterValue([numValue, range[1]]);
			}
		},
		[column, min, range],
	);

	const onToInputChange = React.useCallback(
		(event: React.ChangeEvent<HTMLInputElement>) => {
			const numValue = Number(event.target.value);
			if (!Number.isNaN(numValue) && numValue <= max && numValue >= range[0]) {
				column.setFilterValue([range[0], numValue]);
			}
		},
		[column, max, range],
	);

	const onSliderValueChange = React.useCallback(
		(value: RangeValue) => {
			if (Array.isArray(value) && value.length === 2) {
				column.setFilterValue(value);
			}
		},
		[column],
	);

	const onReset = React.useCallback(
		(event: React.MouseEvent) => {
			if (event.target instanceof HTMLDivElement) {
				event.stopPropagation();
			}
			column.setFilterValue(undefined);
		},
		[column],
	);

	return (
		<Popover>
			<PopoverTrigger asChild>
				<Button variant="outline" size="sm" className="border-dashed">
					{columnFilterValue ? (
						<button
							type="button"
							aria-label={`Clear ${title} filter`}
							className="opacity-70 hover:opacity-100 rounded-sm focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring transition-opacity"
							onClick={onReset}
							onKeyDown={(e) => {
								if (e.key === "Enter" || e.key === " ") {
									e.preventDefault();
									onReset(e as unknown as React.MouseEvent);
								}
							}}
						>
							<XCircle />
						</button>
					) : (
						<PlusCircle />
					)}
					<span>{title}</span>
					{columnFilterValue ? (
						<>
							<Separator
								orientation="vertical"
								className="mx-0.5 data-[orientation=vertical]:h-4"
							/>
							{formatValue(columnFilterValue[0])} -{" "}
							{formatValue(columnFilterValue[1])}
							{unit ? ` ${unit}` : ""}
						</>
					) : null}
				</Button>
			</PopoverTrigger>
			<PopoverContent align="start" className="flex flex-col gap-4 w-auto">
				<div className="flex flex-col gap-4">
					<p className="peer-disabled:opacity-70 font-medium leading-none peer-disabled:cursor-not-allowed">
						{title}
					</p>
					<div className="flex items-center gap-4">
						<Label htmlFor={`${id}-from`} className="sr-only">
							From
						</Label>
						<div className="relative">
							<Input
								id={`${id}-from`}
								type="number"
								aria-valuemin={min}
								aria-valuemax={max}
								inputMode="numeric"
								pattern="[0-9]*"
								placeholder={min.toString()}
								min={min}
								max={max}
								value={range[0]?.toString()}
								onChange={onFromInputChange}
								className={cn("w-24 h-8", unit && "pr-8")}
							/>
							{unit && (
								<span className="top-0 right-0 bottom-0 absolute flex items-center bg-accent px-2 rounded-r-md text-muted-foreground text-sm">
									{unit}
								</span>
							)}
						</div>
						<Label htmlFor={`${id}-to`} className="sr-only">
							to
						</Label>
						<div className="relative">
							<Input
								id={`${id}-to`}
								type="number"
								aria-valuemin={min}
								aria-valuemax={max}
								inputMode="numeric"
								pattern="[0-9]*"
								placeholder={max.toString()}
								min={min}
								max={max}
								value={range[1]?.toString()}
								onChange={onToInputChange}
								className={cn("w-24 h-8", unit && "pr-8")}
							/>
							{unit && (
								<span className="top-0 right-0 bottom-0 absolute flex items-center bg-accent px-2 rounded-r-md text-muted-foreground text-sm">
									{unit}
								</span>
							)}
						</div>
					</div>
					<Label htmlFor={`${id}-slider`} className="sr-only">
						{title} slider
					</Label>
					<Slider
						id={`${id}-slider`}
						min={min}
						max={max}
						step={step}
						value={range}
						onValueChange={onSliderValueChange}
					/>
				</div>
				<Button
					aria-label={`Clear ${title} filter`}
					variant="outline"
					size="sm"
					onClick={onReset}
				>
					Clear
				</Button>
			</PopoverContent>
		</Popover>
	);
}
