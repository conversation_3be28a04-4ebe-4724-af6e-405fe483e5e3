import type { IMember, IProject, ITask } from "./types";

export enum TASK_STATUS {
  TODO = "Todo",
  IN_PROGRESS = "In Progress",
  IN_REVIEW = "In Review",
  DONE = "Done",
  CANCELLED = "Cancelled",
}

export enum TASK_PRIORITY {
  LOW = "Low",
  MEDIUM = "Medium",
  HIGH = "High",
  URGENT = "Urgent",
}

export enum MEMBER_ROLE {
  ADMIN = "Admin",
  PROJECT_MANAGER = "Project Manager",
  DEVELOPER = "Developer",
  DESIGNER = "Designer",
  TESTER = "Tester",
  MEMBER = "Member",
}

export enum MEMBER_STATUS {
  ACTIVE = "Active",
  INACTIVE = "Inactive",
  ON_LEAVE = "On Leave",
}

export enum PROJECT_TYPE {
  WEB_APP = "Web App",
  MOBILE_APP = "Mobile App",
  DESIGN = "Design",
  MARKETING = "Marketing",
  RESEARCH = "Research",
}

export const SAMPLE_MEMBERS_DATA: IMember[] = [
  {
    id: "mb_001",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    avatar: "",
    role: MEMBER_ROLE.PROJECT_MANAGER,
    status: MEMBER_STATUS.ACTIVE,
    department: "Engineering",
    joinedAt: "2023-01-15",
    skills: ["Project Management", "Agile", "Scrum", "Leadership"],
    currentTasksCount: 5,
    completedTasksCount: 87,
    workload: 85,
  },
  {
    id: "mb_002",
    name: "Trần Thị Bình",
    email: "<EMAIL>",
    avatar: "",
    role: MEMBER_ROLE.DEVELOPER,
    status: MEMBER_STATUS.ACTIVE,
    department: "Engineering",
    joinedAt: "2023-03-20",
    skills: ["React", "Node.js", "TypeScript", "MongoDB"],
    currentTasksCount: 3,
    completedTasksCount: 45,
    workload: 70,
  },
  {
    id: "mb_003",
    name: "Lê Minh Châu",
    email: "<EMAIL>",
    avatar: "",
    role: MEMBER_ROLE.DESIGNER,
    status: MEMBER_STATUS.ACTIVE,
    department: "Design",
    joinedAt: "2023-02-10",
    skills: ["UI/UX Design", "Figma", "Adobe Creative Suite", "Prototyping"],
    currentTasksCount: 4,
    completedTasksCount: 32,
    workload: 90,
  },
  {
    id: "mb_004",
    name: "Phạm Văn Dũng",
    email: "<EMAIL>",
    avatar: "",
    role: MEMBER_ROLE.DEVELOPER,
    status: MEMBER_STATUS.ACTIVE,
    department: "Engineering",
    joinedAt: "2022-11-05",
    skills: ["Flutter", "Dart", "Firebase", "iOS Development"],
    currentTasksCount: 2,
    completedTasksCount: 67,
    workload: 60,
  },
  {
    id: "mb_005",
    name: "Hoàng Thị Eka",
    email: "<EMAIL>",
    avatar: "",
    role: MEMBER_ROLE.TESTER,
    status: MEMBER_STATUS.ACTIVE,
    department: "Quality Assurance",
    joinedAt: "2023-04-12",
    skills: ["Manual Testing", "Automation Testing", "Selenium", "API Testing"],
    currentTasksCount: 6,
    completedTasksCount: 28,
    workload: 95,
  },
  {
    id: "mb_006",
    name: "Võ Thanh Phong",
    email: "<EMAIL>",
    avatar: "",
    role: MEMBER_ROLE.DEVELOPER,
    status: MEMBER_STATUS.ON_LEAVE,
    department: "Engineering",
    joinedAt: "2022-08-20",
    skills: ["Python", "Django", "PostgreSQL", "Docker"],
    currentTasksCount: 1,
    completedTasksCount: 93,
    workload: 20,
  },
  {
    id: "mb_007",
    name: "Đặng Thị Giang",
    email: "<EMAIL>",
    avatar: "",
    role: MEMBER_ROLE.DESIGNER,
    status: MEMBER_STATUS.ACTIVE,
    department: "Design",
    joinedAt: "2023-05-08",
    skills: ["Graphic Design", "Branding", "Illustration", "Motion Graphics"],
    currentTasksCount: 3,
    completedTasksCount: 21,
    workload: 75,
  },
  {
    id: "mb_008",
    name: "Bùi Văn Hải",
    email: "<EMAIL>",
    avatar: "",
    role: MEMBER_ROLE.ADMIN,
    status: MEMBER_STATUS.ACTIVE,
    department: "Administration",
    joinedAt: "2022-01-10",
    skills: ["System Administration", "DevOps", "AWS", "Kubernetes"],
    currentTasksCount: 2,
    completedTasksCount: 156,
    workload: 40,
  },
];

export const SAMPLE_TASKS_DATA: ITask[] = [
  {
    id: "tsk_001",
    title: "Thiết kế giao diện trang chủ ứng dụng mobile",
    description:
      "Tạo wireframe và mockup cho trang chủ ứng dụng mobile ecommerce, bao gồm header, banner, danh mục sản phẩm và footer",
    status: TASK_STATUS.IN_PROGRESS,
    priority: TASK_PRIORITY.HIGH,
    assigneeId: "mb_003",
    reporterId: "mb_001",
    projectId: "prj_001",
    projectName: "E-commerce Mobile App",
    projectType: PROJECT_TYPE.MOBILE_APP,
    createdAt: "2024-07-28T09:00:00Z",
    updatedAt: "2024-08-04T14:30:00Z",
    dueDate: "2024-08-10T17:00:00Z",
    estimatedHours: 24,
    actualHours: 16,
    tags: ["UI/UX", "Mobile", "Design"],
    attachments: 3,
    comments: 7,
    subtasksTotal: 4,
    subtasksCompleted: 2,
  },
  {
    id: "tsk_002",
    title: "Implement user authentication API",
    description:
      "Develop REST API endpoints for user registration, login, logout, and password reset functionality with JWT token implementation",
    status: TASK_STATUS.DONE,
    priority: TASK_PRIORITY.URGENT,
    assigneeId: "mb_002",
    reporterId: "mb_001",
    projectId: "prj_001",
    projectName: "E-commerce Mobile App",
    projectType: PROJECT_TYPE.MOBILE_APP,
    createdAt: "2024-07-20T10:15:00Z",
    updatedAt: "2024-08-02T16:45:00Z",
    dueDate: "2024-08-01T17:00:00Z",
    estimatedHours: 16,
    actualHours: 18,
    tags: ["Backend", "API", "Authentication"],
    attachments: 2,
    comments: 12,
    subtasksTotal: 5,
    subtasksCompleted: 5,
  },
  {
    id: "tsk_003",
    title: "Test chức năng thanh toán online",
    description:
      "Kiểm tra toàn bộ flow thanh toán qua VNPay, MoMo và ZaloPay, bao gồm test case happy path và edge cases",
    status: TASK_STATUS.TODO,
    priority: TASK_PRIORITY.HIGH,
    assigneeId: "mb_005",
    reporterId: "mb_001",
    projectId: "prj_001",
    projectName: "E-commerce Mobile App",
    projectType: PROJECT_TYPE.MOBILE_APP,
    createdAt: "2024-08-03T08:30:00Z",
    updatedAt: "2024-08-03T08:30:00Z",
    dueDate: "2024-08-15T17:00:00Z",
    estimatedHours: 20,
    tags: ["Testing", "Payment", "Integration"],
    attachments: 1,
    comments: 2,
    subtasksTotal: 6,
    subtasksCompleted: 0,
  },
  {
    id: "tsk_004",
    title: "Develop Flutter shopping cart module",
    description:
      "Create shopping cart functionality with add/remove items, quantity update, price calculation, and local storage",
    status: TASK_STATUS.IN_REVIEW,
    priority: TASK_PRIORITY.MEDIUM,
    assigneeId: "mb_004",
    reporterId: "mb_001",
    projectId: "prj_001",
    projectName: "E-commerce Mobile App",
    projectType: PROJECT_TYPE.MOBILE_APP,
    createdAt: "2024-07-25T14:20:00Z",
    updatedAt: "2024-08-05T11:15:00Z",
    dueDate: "2024-08-08T17:00:00Z",
    estimatedHours: 28,
    actualHours: 32,
    tags: ["Flutter", "Mobile", "Shopping"],
    attachments: 4,
    comments: 15,
    subtasksTotal: 7,
    subtasksCompleted: 6,
  },
  {
    id: "tsk_005",
    title: "Thiết kế logo và brand identity",
    description:
      "Tạo bộ nhận diện thương hiệu hoàn chỉnh cho startup fintech, bao gồm logo, color palette, typography và guidelines",
    status: TASK_STATUS.IN_PROGRESS,
    priority: TASK_PRIORITY.MEDIUM,
    assigneeId: "mb_007",
    reporterId: "mb_001",
    projectId: "prj_002",
    projectName: "Fintech Startup Branding",
    projectType: PROJECT_TYPE.DESIGN,
    createdAt: "2024-07-30T09:45:00Z",
    updatedAt: "2024-08-05T10:20:00Z",
    dueDate: "2024-08-12T17:00:00Z",
    estimatedHours: 32,
    actualHours: 20,
    tags: ["Branding", "Logo", "Identity"],
    attachments: 8,
    comments: 9,
    subtasksTotal: 5,
    subtasksCompleted: 3,
  },
  {
    id: "tsk_006",
    title: "Setup CI/CD pipeline cho project",
    description:
      "Cấu hình GitHub Actions để tự động build, test và deploy application lên staging và production environment",
    status: TASK_STATUS.DONE,
    priority: TASK_PRIORITY.LOW,
    assigneeId: "mb_008",
    reporterId: "mb_001",
    projectId: "prj_003",
    projectName: "Company Website Redesign",
    projectType: PROJECT_TYPE.WEB_APP,
    createdAt: "2024-07-15T13:00:00Z",
    updatedAt: "2024-07-28T09:30:00Z",
    dueDate: "2024-07-30T17:00:00Z",
    estimatedHours: 12,
    actualHours: 14,
    tags: ["DevOps", "CI/CD", "Automation"],
    attachments: 2,
    comments: 6,
    subtasksTotal: 3,
    subtasksCompleted: 3,
  },
  {
    id: "tsk_007",
    title: "Nghiên cứu thị trường ứng dụng giao hàng",
    description:
      "Phân tích đối thủ cạnh tranh, xu hướng thị trường và nhu cầu khách hàng cho ứng dụng giao hàng tận nơi",
    status: TASK_STATUS.IN_PROGRESS,
    priority: TASK_PRIORITY.MEDIUM,
    assigneeId: "mb_001",
    reporterId: "mb_008",
    projectId: "prj_004",
    projectName: "Delivery App Research",
    projectType: PROJECT_TYPE.RESEARCH,
    createdAt: "2024-08-01T10:00:00Z",
    updatedAt: "2024-08-05T15:45:00Z",
    dueDate: "2024-08-20T17:00:00Z",
    estimatedHours: 40,
    actualHours: 15,
    tags: ["Research", "Market Analysis", "Strategy"],
    attachments: 5,
    comments: 4,
    subtasksTotal: 8,
    subtasksCompleted: 3,
  },
  {
    id: "tsk_008",
    title: "Implement responsive landing page",
    description:
      "Code HTML/CSS/JS cho landing page responsive hỗ trợ desktop, tablet và mobile với animations và SEO optimization",
    status: TASK_STATUS.TODO,
    priority: TASK_PRIORITY.HIGH,
    assigneeId: "mb_002",
    reporterId: "mb_001",
    projectId: "prj_003",
    projectName: "Company Website Redesign",
    projectType: PROJECT_TYPE.WEB_APP,
    createdAt: "2024-08-04T11:30:00Z",
    updatedAt: "2024-08-04T11:30:00Z",
    dueDate: "2024-08-18T17:00:00Z",
    estimatedHours: 24,
    tags: ["Frontend", "Responsive", "SEO"],
    attachments: 3,
    comments: 1,
    subtasksTotal: 6,
    subtasksCompleted: 0,
  },
  {
    id: "tsk_009",
    title: "Database migration và optimization",
    description:
      "Migrate database từ MySQL sang PostgreSQL và optimize các query performance cho hệ thống hiện tại",
    status: TASK_STATUS.CANCELLED,
    priority: TASK_PRIORITY.LOW,
    assigneeId: "mb_006",
    reporterId: "mb_008",
    projectId: "prj_003",
    projectName: "Company Website Redesign",
    projectType: PROJECT_TYPE.WEB_APP,
    createdAt: "2024-07-18T14:15:00Z",
    updatedAt: "2024-08-01T09:20:00Z",
    dueDate: "2024-08-25T17:00:00Z",
    estimatedHours: 36,
    actualHours: 8,
    tags: ["Database", "Migration", "Performance"],
    attachments: 2,
    comments: 8,
    subtasksTotal: 4,
    subtasksCompleted: 1,
  },
  {
    id: "tsk_010",
    title: "Tạo content strategy cho social media",
    description:
      "Lập kế hoạch content 3 tháng cho Facebook, Instagram, LinkedIn và TikTok nhằm tăng brand awareness",
    status: TASK_STATUS.DONE,
    priority: TASK_PRIORITY.MEDIUM,
    assigneeId: "mb_007",
    reporterId: "mb_001",
    projectId: "prj_005",
    projectName: "Social Media Campaign Q3",
    projectType: PROJECT_TYPE.MARKETING,
    createdAt: "2024-07-10T08:00:00Z",
    updatedAt: "2024-07-25T17:30:00Z",
    dueDate: "2024-07-26T17:00:00Z",
    estimatedHours: 16,
    actualHours: 15,
    tags: ["Marketing", "Content", "Social Media"],
    attachments: 12,
    comments: 18,
    subtasksTotal: 5,
    subtasksCompleted: 5,
  },
  {
    id: "tsk_011",
    title: "Automation testing cho mobile app",
    description:
      "Viết test cases và implement automation testing sử dụng Appium cho các chức năng core của mobile app",
    status: TASK_STATUS.IN_PROGRESS,
    priority: TASK_PRIORITY.HIGH,
    assigneeId: "mb_005",
    reporterId: "mb_001",
    projectId: "prj_001",
    projectName: "E-commerce Mobile App",
    projectType: PROJECT_TYPE.MOBILE_APP,
    createdAt: "2024-08-02T09:15:00Z",
    updatedAt: "2024-08-05T13:45:00Z",
    dueDate: "2024-08-16T17:00:00Z",
    estimatedHours: 32,
    actualHours: 12,
    tags: ["Testing", "Automation", "Mobile"],
    attachments: 4,
    comments: 6,
    subtasksTotal: 8,
    subtasksCompleted: 3,
  },
  {
    id: "tsk_012",
    title: "Optimize app performance và memory usage",
    description:
      "Phân tích và tối ưu hiệu suất ứng dụng, giảm memory leak và cải thiện loading time",
    status: TASK_STATUS.TODO,
    priority: TASK_PRIORITY.URGENT,
    assigneeId: "mb_004",
    reporterId: "mb_001",
    projectId: "prj_001",
    projectName: "E-commerce Mobile App",
    projectType: PROJECT_TYPE.MOBILE_APP,
    createdAt: "2024-08-05T10:00:00Z",
    updatedAt: "2024-08-05T10:00:00Z",
    dueDate: "2024-08-12T17:00:00Z",
    estimatedHours: 20,
    tags: ["Performance", "Optimization", "Mobile"],
    attachments: 1,
    comments: 0,
    subtasksTotal: 5,
    subtasksCompleted: 0,
  },
  {
    id: "tsk_013",
    title: "Design user onboarding flow",
    description:
      "Thiết kế flow onboarding cho user mới với các bước welcome, tutorial và setup account",
    status: TASK_STATUS.IN_REVIEW,
    priority: TASK_PRIORITY.MEDIUM,
    assigneeId: "mb_003",
    reporterId: "mb_001",
    projectId: "prj_001",
    projectName: "E-commerce Mobile App",
    projectType: PROJECT_TYPE.MOBILE_APP,
    createdAt: "2024-07-22T11:20:00Z",
    updatedAt: "2024-08-05T16:10:00Z",
    dueDate: "2024-08-09T17:00:00Z",
    estimatedHours: 18,
    actualHours: 16,
    tags: ["UX", "Onboarding", "User Experience"],
    attachments: 6,
    comments: 11,
    subtasksTotal: 4,
    subtasksCompleted: 4,
  },
  {
    id: "tsk_014",
    title: "Implement push notification system",
    description:
      "Tích hợp Firebase Cloud Messaging để gửi push notification cho order updates, promotions và reminders",
    status: TASK_STATUS.TODO,
    priority: TASK_PRIORITY.LOW,
    assigneeId: "mb_002",
    reporterId: "mb_001",
    projectId: "prj_001",
    projectName: "E-commerce Mobile App",
    projectType: PROJECT_TYPE.MOBILE_APP,
    createdAt: "2024-08-04T15:30:00Z",
    updatedAt: "2024-08-04T15:30:00Z",
    dueDate: "2024-08-22T17:00:00Z",
    estimatedHours: 14,
    tags: ["Push Notification", "Firebase", "Integration"],
    attachments: 2,
    comments: 3,
    subtasksTotal: 3,
    subtasksCompleted: 0,
  },
  {
    id: "tsk_015",
    title: "Security audit và penetration testing",
    description:
      "Thực hiện kiểm tra bảo mật toàn diện cho web application, identify vulnerabilities và đưa ra recommendations",
    status: TASK_STATUS.IN_PROGRESS,
    priority: TASK_PRIORITY.URGENT,
    assigneeId: "mb_008",
    reporterId: "mb_001",
    projectId: "prj_003",
    projectName: "Company Website Redesign",
    projectType: PROJECT_TYPE.WEB_APP,
    createdAt: "2024-08-01T13:45:00Z",
    updatedAt: "2024-08-05T14:20:00Z",
    dueDate: "2024-08-14T17:00:00Z",
    estimatedHours: 24,
    actualHours: 10,
    tags: ["Security", "Audit", "Penetration Testing"],
    attachments: 3,
    comments: 5,
    subtasksTotal: 6,
    subtasksCompleted: 2,
  },
];

export const SAMPLE_PROJECTS_DATA: IProject[] = [
  {
    id: "prj_001",
    name: "E-commerce Mobile App",
    type: PROJECT_TYPE.MOBILE_APP,
    description: "Ứng dụng mobile ecommerce đa nền tảng với Flutter",
    startDate: "2024-07-01",
    endDate: "2024-09-30",
    progress: 65,
    membersCount: 5,
    tasksCount: 8,
  },
  {
    id: "prj_002",
    name: "Fintech Startup Branding",
    type: PROJECT_TYPE.DESIGN,
    description: "Thiết kế bộ nhận diện thương hiệu cho startup fintech",
    startDate: "2024-07-15",
    endDate: "2024-08-31",
    progress: 45,
    membersCount: 2,
    tasksCount: 1,
  },
  {
    id: "prj_003",
    name: "Company Website Redesign",
    type: PROJECT_TYPE.WEB_APP,
    description: "Redesign website công ty với công nghệ mới",
    startDate: "2024-07-10",
    endDate: "2024-10-15",
    progress: 30,
    membersCount: 4,
    tasksCount: 4,
  },
  {
    id: "prj_004",
    name: "Delivery App Research",
    type: PROJECT_TYPE.RESEARCH,
    description: "Nghiên cứu thị trường và feasibility cho ứng dụng giao hàng",
    startDate: "2024-08-01",
    endDate: "2024-09-15",
    progress: 20,
    membersCount: 2,
    tasksCount: 1,
  },
  {
    id: "prj_005",
    name: "Social Media Campaign Q3",
    type: PROJECT_TYPE.MARKETING,
    description: "Chiến dịch marketing trên social media quý 3",
    startDate: "2024-07-01",
    endDate: "2024-09-30",
    progress: 85,
    membersCount: 2,
    tasksCount: 1,
  },
];
