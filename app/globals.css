@import "tailwindcss";
@import "tw-animate-css";

:root {
  --background: oklch(0.9903 0.0058 59.6542);
  --foreground: oklch(0.2161 0.0061 56.0434);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.2161 0.0061 56.0434);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.2161 0.0061 56.0434);
  --primary: oklch(0.3084 0.1285 300.5672);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.6898 0.1843 39.8518);
  --secondary-foreground: oklch(0 0 0);
  --muted: oklch(0.9699 0.0013 106.4238);
  --muted-foreground: oklch(0.5534 0.0116 58.0708);
  --accent: oklch(0.9691 0.0161 293.7558);
  --accent-foreground: oklch(0.3084 0.1285 300.5672);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.9232 0.0026 48.7171);
  --input: oklch(0.9510 0.0110 54.4954);
  --ring: oklch(0.3084 0.1285 300.5672);
  --chart-1: oklch(0.3084 0.1285 300.5672);
  --chart-2: oklch(0.6898 0.1843 39.8518);
  --chart-3: oklch(0.9392 0.0737 131.8779);
  --chart-4: oklch(0.6430 0.1989 357.7773);
  --chart-5: oklch(0.6231 0.1880 259.8145);
  --sidebar: oklch(0.9721 0.0068 67.7445);
  --sidebar-foreground: oklch(0.3084 0.1285 300.5672);
  --sidebar-primary: oklch(0.3084 0.1285 300.5672);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.9436 0.0121 67.6801);
  --sidebar-accent-foreground: oklch(0.3084 0.1285 300.5672);
  --sidebar-border: oklch(0.9146 0.0146 64.3315);
  --sidebar-ring: oklch(0.3084 0.1285 300.5672);
  --font-sans: Geist, ui-sans-serif, sans-serif, system-ui;
  --font-serif: Fraunces, ui-serif, serif;
  --font-mono: Geist Mono, ui-monospace, monospace;
  --radius: 1.0rem;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0rem;
  --spacing: 0.25rem;
  --shadow-color: #000000;
  --shadow-opacity: 0.1;
  --shadow-blur: 4px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0px;
  --shadow-offset-y: 2px;
  --letter-spacing: 0rem;
}

.dark {
  --background: oklch(0.1985 0.0446 303.5739);
  --foreground: oklch(0.9789 0.0013 106.4235);
  --card: oklch(0.2417 0.0574 302.3110);
  --card-foreground: oklch(0.9789 0.0013 106.4235);
  --popover: oklch(0.2417 0.0574 302.3110);
  --popover-foreground: oklch(0.9789 0.0013 106.4235);
  --primary: oklch(0.6056 0.2189 292.7172);
  --primary-foreground: oklch(0.1985 0.0446 303.5739);
  --secondary: oklch(0.7073 0.1817 40.3193);
  --secondary-foreground: oklch(0.1985 0.0446 303.5739);
  --muted: oklch(0.2210 0.0513 303.8025);
  --muted-foreground: oklch(0.7161 0.0091 56.2590);
  --accent: oklch(0.3781 0.0761 302.8373);
  --accent-foreground: oklch(0.9789 0.0013 106.4235);
  --destructive: oklch(0.5771 0.2152 27.3250);
  --destructive-foreground: oklch(0.9789 0.0013 106.4235);
  --border: oklch(0.3214 0.0666 302.7743);
  --input: oklch(0.3214 0.0666 302.7743);
  --ring: oklch(0.6056 0.2189 292.7172);
  --chart-1: oklch(0.6056 0.2189 292.7172);
  --chart-2: oklch(0.7073 0.1817 40.3193);
  --chart-3: oklch(0.8003 0.1821 151.7110);
  --chart-4: oklch(0.7253 0.1752 349.7607);
  --chart-5: oklch(0.7137 0.1434 254.6240);
  --sidebar: oklch(0.2417 0.0574 302.3110);
  --sidebar-foreground: oklch(0.9789 0.0013 106.4235);
  --sidebar-primary: oklch(0.7073 0.1817 40.3193);
  --sidebar-primary-foreground: oklch(0.1985 0.0446 303.5739);
  --sidebar-accent: oklch(0.3214 0.0666 302.7743);
  --sidebar-accent-foreground: oklch(0.9789 0.0013 106.4235);
  --sidebar-border: oklch(0.3214 0.0666 302.7743);
  --sidebar-ring: oklch(0.7073 0.1817 40.3193);
  --font-sans: Geist, ui-sans-serif, sans-serif, system-ui;
  --font-serif: Fraunces, ui-serif, serif;
  --font-mono: Geist Mono, ui-monospace, monospace;
  --radius: 1.0rem;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.10);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.10);
  --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.20), 0px 1px 2px -1px hsl(0 0% 0% / 0.20);
  --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.20), 0px 1px 2px -1px hsl(0 0% 0% / 0.20);
  --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.20), 0px 2px 4px -1px hsl(0 0% 0% / 0.20);
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.20), 0px 4px 6px -1px hsl(0 0% 0% / 0.20);
  --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.20), 0px 8px 10px -1px hsl(0 0% 0% / 0.20);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.50);
  --shadow-color: #000000;
  --shadow-opacity: 0.2;
  --shadow-blur: 4px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0px;
  --shadow-offset-y: 2px;
  --letter-spacing: 0rem;
  --spacing: 0.25rem;
}

@theme inline {
  --font-sans: Geist, ui-sans-serif, sans-serif, system-ui;
  --font-mono: Geist Mono, ui-monospace, monospace;
  --font-serif: Fraunces, ui-serif, serif;
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --radius: 1.0rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --shadow-2xl: var(--shadow-2xl);
  --shadow-xl: var(--shadow-xl);
  --shadow-lg: var(--shadow-lg);
  --shadow-md: var(--shadow-md);
  --shadow: var(--shadow);
  --shadow-sm: var(--shadow-sm);
  --shadow-xs: var(--shadow-xs);
  --shadow-2xs: var(--shadow-2xs);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
  --color-destructive-foreground: var(--destructive-foreground);
}

@custom-variant dark (&:is(.dark *));

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
    letter-spacing: var(--tracking-normal);
  }

  /* Fix shadcn cursor pointer not active by default ( WHY??? SHADCN ) */
  button:not([disabled]),
  [role="button"]:not([disabled]) {
    cursor: pointer;
  }
  /* Shadcn's calendar dropdown color fix */
  .dark .rdp-caption_label {
    @apply bg-transparent text-white;
  }

  .dark .rdp-dropdown {
    @apply bg-muted text-white;
  }

  .dark .rdp-nav_button {
    @apply hover:bg-background text-white;
  }

  /* Custom scrollbar styling. Thanks @pranathiperii. */
  ::-webkit-scrollbar {
    width: 5px;
  }
  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 5px;
  }
  * {
    scrollbar-width: thin;
    scrollbar-color: var(--border) transparent;
  }
}

@layer utilities {
  .container {
    @apply mx-auto px-4 md:px-6;
  }
}

/* Animation for dark/light mode transitions */
@keyframes reveal {
  from {
    /* Use CSS variables for the origin, defaulting to center if not set */
    clip-path: circle(0% at var(--x, 50%) var(--y, 50%));
    opacity: 0.7;
  }
  to {
    /* Use CSS variables for the origin, defaulting to center if not set */
    clip-path: circle(150% at var(--x, 50%) var(--y, 50%));
    opacity: 1;
  }
}

/* biome-ignore lint/correctness/noUnknownTypeSelector: view-transition pseudo-element is valid CSS */
::view-transition-new(root) {
  /* Apply the reveal animation */
  animation: reveal 0.4s ease-in-out forwards;
}

/* Animation for loader components*/
@keyframes typing {
  0%,
  100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-2px);
    opacity: 1;
  }
}

@keyframes loading-dots {
  0%,
  100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

@keyframes wave {
  0%,
  100% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(0.6);
  }
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

@keyframes text-blink {
  0%,
  100% {
    color: var(--primary);
  }
  50% {
    color: var(--muted-foreground);
  }
}

@keyframes bounce-dots {
  0%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

@keyframes thin-pulse {
  0%,
  100% {
    transform: scale(0.95);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.4;
  }
}

@keyframes pulse-dot {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.5);
    opacity: 1;
  }
}

@keyframes shimmer-text {
  0% {
    background-position: 150% center;
  }
  100% {
    background-position: -150% center;
  }
}

@keyframes wave-bars {
  0%,
  100% {
    transform: scaleY(1);
    opacity: 0.5;
  }
  50% {
    transform: scaleY(0.6);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: 200% 50%;
  }
  100% {
    background-position: -200% 50%;
  }
}

@keyframes spinner-fade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}